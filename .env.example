# Environment Configuration Template
# Copy this file to .env.local for development or .env.production for production

# API Configuration
VITE_USE_MOCK_SERVER=true
VITE_MOCK_SERVER_URL=http://localhost:3001
VITE_API_BASE_URL=https://your-api-endpoint.com

# Development flags
VITE_DEBUG_API=true
VITE_LOG_LEVEL=debug

# Mock server configuration (development only)
VITE_MOCK_DELAY_MIN=500
VITE_MOCK_DELAY_MAX=1500

# Analytics configuration
VITE_DEFAULT_MERCHANT_ID=your-merchant-id
VITE_DEFAULT_PROVIDER_ID=your-provider-id

# Security
VITE_API_TIMEOUT=30000