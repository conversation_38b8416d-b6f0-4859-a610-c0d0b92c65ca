{"header": {"ID": "{{$guid}}", "application": "76A9FF99-64F9-4F72-9629-305CBE047902"}, "payload": {"userID": "XANDRH004400003", "startDate": "2025-01-01", "endDate": "2025-01-02", "providerId": "56f9cf99-3727-4f2f-bf1c-58dc532ebaf5", "metricIDs": ["rewarded_points", "rewarded_amount", "redeemed_points", "redeemed_amount", "total_transactions", "avg_ticket_per_user", "converted_customers_by_activity", "converted_customers_by_interest", "converted_customers_by_age", "converted_customers_by_gender", "revenue_per_day", "transactions_per_day", "customers_per_day", "transactions_by_geo"], "filterValues": [{"providerId": "56f9cf99-3727-4f2f-bf1c-58dc532ebaf5", "filterId": "interest_type", "value": "customers"}, {"providerId": "56f9cf99-3727-4f2f-bf1c-58dc532ebaf5", "filterId": "age_group_type", "value": "customers"}, {"providerId": "56f9cf99-3727-4f2f-bf1c-58dc532ebaf5", "filterId": "customer_region_type", "value": "home_address"}, {"providerId": "56f9cf99-3727-4f2f-bf1c-58dc532ebaf5", "filterId": "transactions_type", "value": "transactions_count"}, {"providerId": "56f9cf99-3727-4f2f-bf1c-58dc532ebaf5", "filterId": "data_origin", "value": "own_data"}], "metricParameters": {"transactions_by_geo": {"geoType": "region"}}, "merchantId": null}}