# Final Implementation Plan - Analytics Integration

## Agreed Architecture

### **API Call Strategy**
- **Global state management** with Redux/Context
- **One API call per tab** with all required MetricIDs
- **Tab-level loading states** and error handling

### **Data Flow (✅ IMPLEMENTED)**
```
FilterSidebar → Redux Store → useTabData Hook → Analytics Service → API/Dev Server → Charts
     ↓              ↓              ↓                    ↓                  ↓            ↓
  Apply Filters → Store State → Pass Filters → Transform Filters → Filter Data → Display
```

**Filter Integration Flow:**
```
UI Filters → Filter Mapping Service → API Filters → Dev Server → Filtered Data → Charts
     ↓                ↓                      ↓             ↓             ↓           ↓
{gender:'female'} → [{filterId:'gender',   → Data        → Only Female → Updated
                    value:'["f"]'}]        Generation     Data Returned  Display
```

### **Transformation Strategy: Option C (Service Layer)**
Transform data in service layer before storing in global state:
- Centralized transformation logic
- Better performance (transform once, not on every render)
- Consistent data format across the application

## Implementation Structure

### 1. **Global State Management**
```javascript
// State structure
{
  analytics: {
    dashboard: {
      data: { totalRevenue: {...}, totalTransactions: {...} },
      loading: false,
      error: null
    },
    revenue: {
      data: { metrics: {...}, charts: {...} },
      loading: false,
      error: null
    },
    demographics: { ... },
    competition: { ... }
  },
  filters: {
    dateRange: { startDate: '2025-01-01', endDate: '2025-01-31' },
    merchantId: 'uuid',
    uiFilters: {
      gender: 'all',
      ageGroups: [],
      shoppingInterests: [],
      customerLocation: [],
      channel: 'all',
      goForMore: null
    },
    filterValues: [
      { providerId: 'uuid', filterId: 'gender', value: '["f"]' }
    ],
    filtersChanged: false
  }
}
```

### 2. **Service Layer with Transformation**
```javascript
// analyticsService.js
class AnalyticsService {
  async fetchTabData(tabName, metricIDs, filters) {
    const rawResponse = await this.queryAnalytics(request);
    const transformedData = this.transformTabData(tabName, rawResponse);
    return transformedData;
  }
  
  transformTabData(tabName, response) {
    // Tab-specific transformation logic
    switch(tabName) {
      case 'dashboard':
        return this.transformDashboardData(response);
      case 'revenue':
        return this.transformRevenueData(response);
      // ...
    }
  }
}
```

### 3. **Tab-Level Data Hooks**
```javascript
// hooks/useTabData.js
export const useTabData = (tabName, metricIDs) => {
  const dispatch = useDispatch();
  const tabData = useSelector(state => state.analytics[tabName]);
  const filters = useSelector(state => state.filters);
  
  useEffect(() => {
    dispatch(fetchTabDataThunk(tabName, metricIDs, filters));
  }, [tabName, filters]);
  
  return tabData;
};
```

### 4. **Graceful Error Handling**
```javascript
// Component rendering with partial failures
{tabData.data.totalRevenue ? (
  <MetricCard data={tabData.data.totalRevenue} />
) : (
  <MetricCard 
    error="Unable to load revenue data" 
    showErrorState={true} 
  />
)}
```

## Detailed Implementation Steps

### Phase 1: Core Infrastructure
1. **Set up Redux store** with analytics and filters slices
2. **Create service layer** with transformation functions
3. **Build tab-level hooks** for data fetching
4. **Implement error handling** patterns

### Phase 2: Tab-Specific Implementation
1. **Dashboard Tab**
   - MetricIDs: `['total_revenue', 'total_transactions', 'avg_ticket_per_user']`
   - Transform to: `{ totalRevenue: {merchant, competitor}, ... }`
   - Components: UniversalMetricCard × 3, TimeSeriesChart × 3

2. **Revenue Tab**
   - MetricIDs: `['total_revenue', 'rewarded_amount', 'redeemed_amount', 'revenue_per_day']`
   - Transform to: `{ metrics: {...}, timeSeries: {...}, breakdowns: {...} }`
   - Components: MetricCards, TimeSeriesChart, BreakdownCharts

3. **Demographics Tab**
   - MetricIDs: `['converted_customers_by_age', 'converted_customers_by_gender', 'converted_customers_by_interest']`
   - Transform to: `{ demographics: {...}, charts: {...} }`
   - Components: MetricCards, Demographic charts

4. **Competition Tab**
   - MetricIDs: `['total_revenue', 'transactions_per_day', 'revenue_per_day']` + competition filter
   - Transform to: `{ comparison: {...}, trends: {...} }`
   - Components: Competition cards, Timeline, Heatmap

### Phase 3: Global Features ✅ COMPLETED
1. **✅ Global filter management** (date range, merchant selection, demographic filters)
2. **Period-over-period calculations** (separate API calls) - Pending
3. **✅ Loading states** and error boundaries
4. **✅ Performance optimization** (memoized selectors, active tab refresh only)

## ✅ FILTER INTEGRATION ARCHITECTURE

### **Core Components**

#### **1. Filter Mapping Service** (`src/services/filterMappingService.js`)
```javascript
// Bidirectional UI ↔ API filter conversion
class FilterMappingService {
  mapUIFiltersToAPI(uiFilters) {
    // Convert {gender: 'female'} → [{filterId: 'gender', value: '["f"]'}]
  }
  mapAPIFiltersToUI(filterValues) {
    // Convert API format back to UI format for persistence
  }
  validateDatasetSize(data) {
    // Check if filtered data meets minimum requirements
  }
}
```

#### **2. Enhanced Redux State** (`src/store/slices/filtersSlice.js`)
```javascript
// Dual filter state management
{
  uiFilters: {           // User-friendly format
    gender: 'female',
    ageGroups: ['millennials'],
    shoppingInterests: ['SHOPINT1']
  },
  filterValues: [        // API-compatible format
    { providerId: 'uuid', filterId: 'gender', value: '["f"]' }
  ],
  filtersChanged: true,  // Triggers refresh
  selectedTab: 'dashboard'
}
```

#### **3. Smart Data Refresh** (`src/hooks/useTabData.js`)
```javascript
// Only refresh active tab when filters change
useEffect(() => {
  if (filtersChanged && selectedTab === tabName) {
    fetchData();
    dispatch(markFiltersApplied());
  }
}, [filtersChanged, selectedTab, tabName]);
```

#### **4. Filter-Aware Mock Server** (`mock-server/utils/filterAwareDataGenerator.js`)
```javascript
// Applies filters to generated data
function generateFilterAwareMetric(metricID, options) {
  const baseData = generateMetricResponse(metricID, options);
  const filteredData = applyFiltersToData(metricID, baseData, parsedFilters);
  return validateDatasetSize(filteredData) ? filteredData : insufficientDataResponse;
}
```

## Transformation Examples

### Dashboard Data Transformation
```javascript
// Raw API Response
{
  "payload": {
    "metrics": [
      { "metricID": "total_revenue", "scalarValue": "1234567.89", "merchantId": "ATTICA" },
      { "metricID": "total_revenue", "scalarValue": "987654.32", "merchantId": "competition" },
      { "metricID": "total_transactions", "scalarValue": "45678", "merchantId": "ATTICA" }
    ]
  }
}

// Transformed Output
{
  totalRevenue: {
    merchant: { value: 1234567.89, change: null, valueType: 'currency' },
    competitor: { value: 987654.32, change: null, valueType: 'currency' }
  },
  totalTransactions: {
    merchant: { value: 45678, change: null, valueType: 'number' },
    competitor: null // No competition data available
  }
}
```

### Error Handling Examples
```javascript
// Partial Success Response
{
  totalRevenue: { merchant: {...}, competitor: {...} },
  totalTransactions: { error: "Metric unavailable", showError: true },
  avgTransaction: { merchant: {...}, competitor: null }
}

// Component Rendering
<MetricCard 
  data={data.totalTransactions} 
  error={data.totalTransactions?.error}
  showErrorState={data.totalTransactions?.showError}
/>
```

## File Structure
```
src/
├── store/
│   ├── slices/
│   │   ├── analyticsSlice.js
│   │   └── filtersSlice.js
│   └── index.js
├── services/
│   ├── analyticsService.js
│   └── transformations/
│       ├── dashboardTransform.js
│       ├── revenueTransform.js
│       └── index.js
├── hooks/
│   ├── useTabData.js
│   ├── useDashboardData.js
│   └── useGlobalFilters.js
├── components/
│   ├── dashboard/Dashboard.jsx
│   ├── revenue/Revenue.jsx
│   └── ui/
│       ├── ErrorBoundary.jsx
│       └── LoadingSpinner.jsx
└── utils/
    ├── apiHelpers.js
    └── formatters.js
```

## Benefits of This Approach

1. **Clear Separation**: Service handles API, hooks handle state, components handle UI
2. **Reusable**: Transformation logic can be shared and tested independently
3. **Performance**: Transform once in service, use many times in components
4. **Maintainable**: Each layer has single responsibility
5. **Scalable**: Easy to add new tabs, metrics, or transformations
6. **Error Resilient**: Graceful handling of partial failures
7. **Global Consistency**: Filters and date ranges apply everywhere

## Next Steps

1. ✅ **Approve this plan** and any adjustments
2. 🔧 **Implement Redux store** and slices
3. 🔧 **Build service transformation layer**
4. 🔧 **Create tab-level hooks**
5. 🔧 **Update components** to use new data flow
6. 🧪 **Test integration** with mock server
7. 📚 **Update documentation**

Ready to proceed with implementation?