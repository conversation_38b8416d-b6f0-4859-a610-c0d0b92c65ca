/* Tailwind Component Classes */
.chart-container {
  @apply h-[350px] mb-4 bg-nbg-primary rounded-lg text-white flex items-center justify-center font-bold;
}

.heatmap-cell {
  @apply w-6 h-6 m-0.5 rounded-sm flex items-center justify-center text-xs font-medium border border-gray-200 text-white lg:w-8 lg:h-8 lg:m-1 lg:text-sm;
}

/* Heatmap color classes - keeping as utilities for dynamic usage */
.high {
  @apply bg-green-800 text-white;
}

.good {
  @apply bg-green-600 text-white;
}

.medium {
  @apply bg-red-300 text-white;
}

.low {
  @apply bg-red-600 text-white;
}

.out-of-period {
  @apply bg-gray-100 text-white;
}

.future {
  @apply bg-gray-50 text-gray-400;
}

.empty {
  @apply bg-transparent border-0 text-gray-700;
}

/* Reusable component classes */
.stat-card {
  @apply bg-white rounded-lg p-4 shadow-sm border border-gray-200;
}

.icon-circle {
  @apply w-10 h-10 rounded-full flex items-center justify-center mr-3;
}

.section-card {
  @apply bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200;
}

.section-header {
  @apply p-4 border-b border-gray-200 flex items-center justify-between;
}

.section-content {
  @apply p-4;
}

.map-container {
  @apply h-[350px] w-full flex justify-center items-center mb-4;
}

.map-svg {
  @apply max-h-full w-auto;
}

/* Chart specific classes */
.chart-container-custom {
  @apply bg-white rounded-lg border border-gray-200 p-4 mb-6;
}

.chart-header {
  @apply flex justify-between items-center mb-4 flex-wrap gap-4;
}

.chart-title {
  @apply text-lg font-semibold text-gray-900;
}

.chart-controls-group {
  @apply flex gap-4 flex-wrap;
}

.chart-controls {
  @apply md:flex-row md:items-center md:space-y-0 flex flex-col items-stretch space-y-2;
}

.chart-controls > div {
  @apply md:mb-0 mb-2;
}

.chart-controls button {
  @apply text-sm px-3 py-2;
}

/* Chart responsive styles - keeping as regular CSS for Recharts compatibility */
.recharts-wrapper {
  width: 100% !important;
}